from enum import StrEnum
from typing import TypeAlias

IOTRON_DEFAULT_AGREEMENT_TYPE = "Standard"
IOTRON_DEFAULT_AGREEMENT_STATUS = "Received from S&B"

# Booleans
IOTRONBoolString: TypeAlias = str

IOTRON_YES_VALUE: IOTRONBoolString = "YES"
IOTRON_NO_VALUE: IOTRONBoolString = "NO"

# Collections
IOTRON_ALL_VALUE = "ALL"

IOTRON_ALL_PMNS_VALUE = [IOTRON_ALL_VALUE]


class IOTRONServiceTypeEnum(StrEnum):
    VOICE = "Voice"
    SMS = "SMS"
    DATA = "Data"
    VOLTE = "VoLTE"
    ACCESS_FEE = "Access Fee"


class IOTRONEventTypeEnum(StrEnum):
    MO = "MO"
    MT = "MT"
    MB = "MB"
    IMSI = "IMSI"
    MO_MT = "MO & MT"


class IOTRONDiscountDirectionEnum(StrEnum):
    BIDIRECTIONAL = "Bi-Directional"
    INBOUND = "Inbound"
    OUTBOUND = "Outbound"


class IOTRONDiscountCalculationTypeEnum(StrEnum):
    SINGLE_RATE_EFFECTIVE = "Single Rate Effective (Voice, SMS, Data)"

    ALL_YOU_CAN_EAT = "All You Can Eat (Voice, SMS, Data, All Services)"
    ALL_YOU_CAN_EAT_DAILY = "All You Can Eat Daily (Data only)"
    ALL_YOU_CAN_EAT_MONTHLY = "All You Can Eat Monthly (Data only)"

    BACK_TO_FIRST = "Back to First (Voice, SMS, Data)"

    CONTRIBUTION_TO_GROUP_SHORTFALL = "Contribution to Group Shortfall (Voice, SMS, Data)"

    SEND_OR_PAY_FINANCIAL = "Send or Pay Financial (All Services)"
    SEND_OR_PAY_FINANCIAL_ACCESS_FEE = "Send or Pay Financial (Access Fee)"

    SEND_OR_PAY_TRAFFIC = "Send or Pay Traffic (Voice, SMS, Data)"

    STEPPED_TIERED = "Stepped/Tiered (Voice, SMS, Data)"
    STEPPED_TIERED_MONTHLY = "Stepped/Tiered Monthly (Data only)"

    PER_MONTH_PER_IMSI = "Per Month Per IMSI (All Services)"
    PER_MONTH_PER_IMSI_ABOVE_THRESHOLD = "Per Month Per IMSI - Above Threshold (All Services)"
    PER_MONTH_PER_IMSI_STEPPED_TIERED = "Per Month Per IMSI - Stepped/Tiered (Data)"
    PER_MONTH_PER_IMSI_BACK_TO_FIRST = "Per Month Per IMSI - Back to First (All Services)"
    PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING = "Per Month Per IMSI With Incremental Charging (Data)"

    PER_DAY_PER_IMSI_ALL_YOU_CAN_EAT = "Per Day Per IMSI – All You Can Eat (Data only)"
    PER_DAY_PER_IMSI_CAPPED_CHARGE = "Per Day Per IMSI – Capped Charge (Data only)"
    PER_DAY_PER_IMSI_CAPPED_CHARGE_HIGHEST = "Per Day Per IMSI – Capped Charge (highest ‘X%’ of users) (Data only)"


class IOTRONDiscountBasisEnum(StrEnum):
    VALUE = "Value"
    PERCENTAGE = "Percentage"
    NET_TRAFFIC_SENDER_TAP_RATE = "Net Traffic Sender TAP Rate"
    NET_TRAFFIC_RECEIVER_TAP_RATE = "Net Traffic Receiver TAP Rate"
    DEDUCTION = "Deduction"
    AVERAGE_IOT_RATE = "Average IOT Rate"


class IOTRONDiscountBoundTypeEnum(StrEnum):
    VOLUME = "V"
    FINANCIAL_COMMITMENT = "F"
    MARKET_SHARE = "M"
    UNIQUE_IMSI_COUNT_PER_MONTH = "I"
    VOLUME_INCLUDED_IN_ACCESS_FEE = "A"
    FINANCIAL_THRESHOLD = "H"


class IOTRONDiscountBalancingEnum(StrEnum):
    BALANCED = "Balanced"

    NO_BALANCING = "No-Balancing"
    # The same as NO_BALANCING, but used only for agreement submission to cover IOTRON's corner case
    NO_BALANCING_SPECIFIED = "No Balancing Specified"

    # The same as UNBALANCED, but using only for agreement submission to cover IOTRON's corner case
    DASHED_UNBALANCED = "Un-Balanced"
    UNBALANCED = "Unbalanced"

    BALANCED_WITH_GROUP_COMPENSATION = "Balanced with Group Compensation"
    BALANCING_FOR_SOP_TRAFFIC = "Balancing for SoP Traffic"
    INCREMENTAL_TRAFFIC = "Incremental Traffic (Year on Year)"
    UNBALANCED_WITH_GROUP_COMPENSATION = "Un-Balanced with Group Compensation"


class IOTRONDiscountQualifyingBasisEnum(StrEnum):
    VOLUME = "V"
    MARKET_SHARE_PERCENTAGE = "M"
    UNIQUE_IMSI_COUNT_PER_MONTH = "U"
    AVERAGE_MONTHLY_USAGE_PER_IMSI = "A"


class IOTRONCallDestinationEnum(StrEnum):
    HOME = "Home"
    LOCAL = "Local"
    INTERNATIONAL = "International"
